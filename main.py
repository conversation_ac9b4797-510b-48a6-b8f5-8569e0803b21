#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国股指期货报告自动生成系统 - 主程序入口
Version: MVP-V1.0
Author: AI Assistant
Date: 2025-08-04
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from loguru import logger
    from config.config import *
    from utils.logger_setup import setup_logger
    from utils.directory_manager import ensure_directories
    from modules.chart_generator import ChartGenerator
    from modules.article_fetcher import ArticleFetcher
    from modules.report_composer import ReportComposer
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


class ReportGeneratorMain:
    """报告生成系统主控制器"""
    
    def __init__(self):
        """初始化主控制器"""
        self.start_time = datetime.now()
        self.setup_environment()
        self.initialize_modules()
        
    def setup_environment(self):
        """设置运行环境"""
        try:
            # 设置日志
            setup_logger()
            logger.info("=" * 60)
            logger.info("中国股指期货报告自动生成系统启动")
            logger.info(f"版本: MVP-V1.0")
            logger.info(f"启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("=" * 60)
            
            # 确保输出目录存在
            ensure_directories()
            logger.info("输出目录检查完成")
            
            # 验证配置
            validate_config()
            logger.info("配置验证通过")
            
        except Exception as e:
            logger.error(f"环境设置失败: {e}")
            raise
    
    def initialize_modules(self):
        """初始化各功能模块"""
        try:
            logger.info("正在初始化功能模块...")
            
            # 初始化图表生成模块
            self.chart_generator = ChartGenerator()
            logger.info("✓ 图表生成模块初始化完成")
            
            # 初始化文章获取模块
            self.article_fetcher = ArticleFetcher()
            logger.info("✓ 文章获取模块初始化完成")
            
            # 初始化报告编排模块
            self.report_composer = ReportComposer()
            logger.info("✓ 报告编排模块初始化完成")
            
            logger.info("所有模块初始化完成")
            
        except Exception as e:
            logger.error(f"模块初始化失败: {e}")
            raise
    
    def run_chart_generation(self):
        """执行图表生成流程"""
        logger.info("开始执行图表生成流程...")
        
        try:
            # 检查Wind连接
            if not self.chart_generator.check_wind_connection():
                logger.error("Wind连接失败，请确保Wind终端已启动并登录")
                return False
            
            # 生成所有图表
            chart_results = self.chart_generator.generate_all_charts()
            
            if chart_results:
                logger.info(f"✓ 图表生成完成，共生成 {len(chart_results)} 张图表")
                return chart_results
            else:
                logger.error("图表生成失败")
                return False
                
        except Exception as e:
            logger.error(f"图表生成过程中出错: {e}")
            return False
    
    def run_article_fetching(self):
        """执行文章获取流程"""
        logger.info("开始执行文章获取流程...")
        
        try:
            # 提示用户准备登录
            print("\n" + "="*50)
            print("📱 请准备登录微信公众平台")
            print("系统将自动打开浏览器，请按提示完成登录")
            print("="*50)
            
            input("按回车键继续...")
            
            # 获取文章
            article_results = self.article_fetcher.fetch_all_articles()
            
            if article_results:
                logger.info(f"✓ 文章获取完成，共获取 {len(article_results)} 篇文章")
                return article_results
            else:
                logger.warning("未获取到文章，将使用模拟数据继续")
                return []
                
        except Exception as e:
            logger.error(f"文章获取过程中出错: {e}")
            return []
    
    def run_report_composition(self, chart_results, article_results):
        """执行报告编排流程"""
        logger.info("开始执行报告编排流程...")
        
        try:
            # 编排生成报告
            report_result = self.report_composer.compose_report(
                chart_data=chart_results,
                article_data=article_results
            )
            
            if report_result:
                logger.info("✓ 报告生成完成")
                return report_result
            else:
                logger.error("报告生成失败")
                return False
                
        except Exception as e:
            logger.error(f"报告生成过程中出错: {e}")
            return False
    
    def run_full_pipeline(self):
        """执行完整的报告生成流程"""
        logger.info("开始执行完整报告生成流程")
        
        try:
            # 步骤1: 生成图表
            print("\n🔄 步骤 1/3: 生成量化图表...")
            chart_results = self.run_chart_generation()
            if not chart_results:
                logger.error("图表生成失败，流程终止")
                return False
            
            # 步骤2: 获取文章
            print("\n🔄 步骤 2/3: 获取舆情文章...")
            article_results = self.run_article_fetching()
            
            # 步骤3: 生成报告
            print("\n🔄 步骤 3/3: 生成最终报告...")
            report_result = self.run_report_composition(chart_results, article_results)
            if not report_result:
                logger.error("报告生成失败，流程终止")
                return False
            
            # 流程完成
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            logger.info("=" * 60)
            logger.info("🎉 报告生成流程全部完成！")
            logger.info(f"总耗时: {duration}")
            logger.info(f"输出目录: {OUTPUT_DIRS['base']}")
            logger.info("=" * 60)
            
            # 显示输出文件信息
            self.show_output_summary(report_result)
            
            return True
            
        except Exception as e:
            logger.error(f"完整流程执行失败: {e}")
            return False
    
    def show_output_summary(self, report_result):
        """显示输出文件摘要"""
        print("\n📁 输出文件摘要:")
        print("-" * 40)
        
        # 显示图表文件
        images_dir = Path(OUTPUT_DIRS['images'])
        if images_dir.exists():
            image_files = list(images_dir.glob("*.html")) + list(images_dir.glob("*.png"))
            print(f"📊 图表文件: {len(image_files)} 个")
            for file in image_files[:5]:  # 只显示前5个
                print(f"   - {file.name}")
            if len(image_files) > 5:
                print(f"   ... 还有 {len(image_files) - 5} 个文件")
        
        # 显示文章文件
        articles_dir = Path(OUTPUT_DIRS['articles'])
        if articles_dir.exists():
            article_files = list(articles_dir.glob("*.txt"))
            print(f"📰 文章文件: {len(article_files)} 个")
            for file in article_files[:3]:  # 只显示前3个
                print(f"   - {file.name}")
            if len(article_files) > 3:
                print(f"   ... 还有 {len(article_files) - 3} 个文件")
        
        # 显示报告文件
        if report_result and 'file_path' in report_result:
            print(f"📋 最终报告: {Path(report_result['file_path']).name}")
        
        print("-" * 40)


def main():
    """主函数"""
    try:
        # 创建主控制器
        generator = ReportGeneratorMain()
        
        # 执行完整流程
        success = generator.run_full_pipeline()
        
        if success:
            print("\n✅ 程序执行成功！")
            return 0
        else:
            print("\n❌ 程序执行失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序执行")
        return 1
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
